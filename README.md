# Multiverse

## Installation & Setup

### Prerequisites

- **Node.js**
- **pnpm**

### Install PNPM through Corepack

```sh
npm install --global corepack@latest
```

```sh
corepack enable pnpm
```

```sh
pnpm install
```


## First Steps Guide

[Constellation Quick Start Guide](https://www.notion.so/drata/Constellation-Contributor-s-Quick-Start-Guide-187095aed4f180e9b4d9dcd5c96ab73c)

## TL;DR - (More detailed info further down)

### Structure

- 5 common kinds of Projects:
    - Components
        - A collection of react components scoped to some feature or sub-feature
        - Ex: `frameworks-components`, `workflow-components`
    - Models
        - Classes & State to be worked upon.
        - Most come directly from the API.
        - Ex: Entities, DTOs, Global State (formerly in `redux`), Local State (formerly in `useState`).
    - Views
        - Combines other Models, Controllers, and components into a ready-to-be-displayed single item.
    - Controllers
        - Business-logic, transformations, and callbacks
        - Consumes & manipulates `Model`s
    - Apps
        - Final collection of `Model`s, `View`s, and `Controller`s
        - Cannot be imported or consumed by anything else
        - This is what gets built and deployed

### Most common commands

> **Note**: Make sure you've completed the [Installation & Setup](#installation--setup) steps first.

- Run Drata in Dev Mode
    ```sh
    pnpm run app:drata:dev
    ```

- Quick start (install dependencies + start dev server)
    ```sh
    pnpm run qs
    ```

- Run unit tests
    ```sh
    # Run all unit tests
    pnpm run test
    ```

- Linting (also runs automatically when needed)
    ```sh
    # Lint all files
    pnpm run lint
    ```

- Format code
    ```sh
    # Format all files
    pnpm run format
    ```

## Available Scripts

This monorepo uses PNPM workspaces and has various scripts defined in the root `package.json` for different development tasks.

### See all available scripts

```sh
pnpm run
```

### Quick Start Command

```sh
# Install dependencies, update API SDK, generate tokens, and start dev server
pnpm run qs
```

## Generating new Projects

Nobody likes having to write boilerplate, so skip right to the useful parts by making use of our generators!

These generators are interactive and will ask you questions so that it makes the right stuff.

```sh
# Anything
pnpm run generate  # answer questions

# Components
pnpm run generate:component # answer questions

# Models
pnpm run generate:model  # answer questions

# Views
pnpm run generate:view  # answer questions

# Controllers
pnpm run generate:controller  # answer questions

# Helpers
pnpm run generate:helper  # answer questions

# Global modules
pnpm run generate:global  # answer questions

# Cosmos-Lab Component (remember that @cosmos-lab components are meant to be generic and used everywhere)
pnpm run generate:cosmos-lab  # answer questions

# @ui Component (remember that @ui components are meant to be generic and used everywhere)
pnpm run generate:ui  # answer questions
```

## Code Structure & MVC

This is a monorepo that uses a custom modular MVC architecture built on PNPM workspaces. The MVC pattern organizes code for better understanding, testing, and maintenance.

The MVC pattern consists of three main parts:

- **M**odels - Data containers with computed properties and business logic
- **V**iews - React components that represent complete user interfaces
- **C**ontrollers - Business logic that connects Models to APIs using MobX reactivity

In this repo, we've extended the MVC pattern to include additional module types:

- **Components** - Reusable UI components for specific business domains
- **Helpers** - Pure utility functions and formatters
- **Globals** - Shared state, configuration, and cross-cutting concerns
- **UI** - Generic, reusable UI components used across the application
- **Cosmos/Cosmos-Lab** - Design system components

Each module is a separate PNPM workspace package with its own `package.json`, allowing for clear dependency management and modular development.

## Components

This is a simple collection of react components that will eventually be imported into a View. They are scoped to indicate what kind of feature or sub-feature they are intended to display. You can make these liberally, even down to sub-sub-sub-sub-sub-features.

There are some _special_ components like `cosmos` and `ui` that are meant to be hyper-generic and used across everything.

Remember that **all** state and actions must be in Models and Controllers. React components in this repo should only focus on _displaying_.

## M - Models

#### Project Naming pattern: `some-thing-model`

#### File naming pattern: `some-thing.model.ts`

Models are containers of state that are manipulated by Controllers. They often provide computed properties and data transformations for Views. Most API data types come from the auto-generated API SDK, but Models add business logic and computed properties on top of that data.

Models are there to store data and provide computed properties, so they should be a pure collection of properties with computed getters. Actions should be in Controllers.

```ts
import { sharedCurrentUserController } from '@globals/current-user';
import { makeAutoObservable } from '@globals/mobx';

export class MyDrataPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'my-drata';
    isCentered = true;

    // computed properties ARE allowed and encouraged
    get title(): string {
        const { user, isLoading } = sharedCurrentUserController;

        if (isLoading || !user) {
            return '';
        }

        return `Hi ${user.firstName}, welcome to Drata!`;
    }

    // simple properties and getters are fine
    get userSelect(): ListBoxItems {
        return sharedUsersController.fullUsers;
    }

    // actions ARE NOT allowed - put these in Controllers
    // async save() {
    //     await api.updateSomeThing(this);
    // }
}

export const sharedMyDrataPageHeaderModel = new MyDrataPageHeaderModel();
```

## V - Views

Views are React components that combine Models, Controllers, and other components into complete user interfaces. They are typically imported by Apps and represent full pages or major sections of the application.

Views _almost_ map 1-to-1 with Routes. However, they also include tab contents, modals, wizard steps, and everything else that could be considered a unit of Content.

Use the rule-of-thumb: "Did the URL change? Then I need a new view."

```tsx
import { observer } from '@globals/mobx';
import { sharedVendorsCurrentController } from '@controllers/vendors';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { sharedVendorsCurrentModel } from '@models/vendors-profile';
import { useNavigate } from '@remix-run/react';

export const VendorsCurrentView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const { allVendorsCurrent, loadVendorsCurrent, isLoading } =
        sharedVendorsCurrentController;

    return (
        <Grid gap="lg" data-testid="VendorsCurrentView" data-id="zsTXJABO">
            <Datatable
                isLoading={isLoading}
                tableId="datatable-vendors-current"
                data={allVendorsCurrent.data}
                columns={VENDORS_CURRENT_COLUMNS}
                total={allVendorsCurrent.total}
                filterProps={sharedVendorsCurrentModel.filters}
                onFetchData={loadVendorsCurrent}
                onRowClick={({ row }) => {
                    navigate(`${row.id}/overview`);
                }}
            />
        </Grid>
    );
});
```

## C - Controllers

Controllers are the life-blood of the repo. They are how you will implement all your business logic and glue your React components to your Models and API.

Just like with Models, they are also MobX classes and offer you the same reactive capabilities. Controllers use `ObservedQuery` and `ObservedMutation` to interact with the API SDK.

```ts
import { companiesControllerGetCompanyWithKeyPersonnelOptions } from '@globals/api-sdk/queries';
import type { KeyPersonnelResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class OrganizationKeyPersonnelController {
    constructor() {
        makeAutoObservable(this);
    }

    // Private queries using # syntax
    #organizationKeyPersonnelQuery = new ObservedQuery(
        companiesControllerGetCompanyWithKeyPersonnelOptions,
    );

    // Computed getters for accessing data
    get organizationKeyPersonnel(): KeyPersonnelResponseDto | null {
        return this.#organizationKeyPersonnelQuery.data;
    }

    get isLoading(): boolean {
        return this.#organizationKeyPersonnelQuery.isLoading;
    }

    // Action methods for triggering API calls
    getOrganizationKeyPersonnel = () => {
        this.#organizationKeyPersonnelQuery.load();
    };

    // For queries with parameters
    loadUserDevices = (userId: number) => {
        this.#userDevicesQuery.load({
            path: { userId },
        });
    };
}

// Export as shared singleton
export const sharedOrganizationKeyPersonnelController =
    new OrganizationKeyPersonnelController();
```

### Controllers with Mutations

For API operations that modify data, use `ObservedMutation`:

```ts
import {
    publicApiKeysControllerCreatePublicApiKeyMutationOptions,
    publicApiKeysControllerDeletePublicApiKeyMutationOptions,
} from '@globals/api-sdk/queries';
import type { PublicApiKeyRequestDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedMutation, ObservedQuery } from '@globals/mobx';

class ApiKeysController {
    constructor() {
        makeAutoObservable(this);
    }

    #createApiKeyMutation = new ObservedMutation(
        publicApiKeysControllerCreatePublicApiKeyMutationOptions,
    );

    #deleteApiKeyMutation = new ObservedMutation(
        publicApiKeysControllerDeletePublicApiKeyMutationOptions,
    );

    get isCreating(): boolean {
        return this.#createApiKeyMutation.isPending;
    }

    get createError(): Error | null {
        return this.#createApiKeyMutation.error;
    }

    createApiKey = async (data: PublicApiKeyRequestDto) => {
        await this.#createApiKeyMutation.mutateAsync({
            body: data,
        });
    };

    deleteApiKey = async (id: string) => {
        await this.#deleteApiKeyMutation.mutateAsync({
            path: { id },
        });
    };
}

export const sharedApiKeysController = new ApiKeysController();
```

## API SDK Integration

The API SDK is auto-generated from the backend OpenAPI specification and provides type-safe API functions. Here are the key import patterns:

### API Functions and Types

```ts
// Import API functions from queries sub-path
import {
    companiesControllerGetCompanyWithKeyPersonnelOptions,
    publicApiKeysControllerCreatePublicApiKeyOptions,
} from '@globals/api-sdk/queries';

// Import TypeScript types from types sub-path
import type {
    KeyPersonnelResponseDto,
    PublicApiKeyRequestDto,
} from '@globals/api-sdk/types';

// Import Zod schemas for validation from zod sub-path
import { zPublicApiKeyRequestDto } from '@globals/api-sdk/zod';
```

### MobX Integration

The API SDK includes custom MobX wrappers that automatically generate reactive queries:

```ts
import { makeAutoObservable, ObservedQuery, ObservedMutation } from '@globals/mobx';
import { companiesControllerGetCompanyWithKeyPersonnelOptions, publicApiKeysControllerCreatePublicApiKeyMutationOptions } from '@globals/api-sdk/queries';

// ObservedQuery automatically creates reactive MobX queries
const query = new ObservedQuery(companiesControllerGetCompanyWithKeyPersonnelOptions);

// ObservedMutation automatically creates reactive MobX mutations
const mutation = new ObservedMutation(publicApiKeysControllerCreatePublicApiKeyMutationOptions);
```

## Run Storybook for cosmos components

```sh
pnpm run storybook
```

## Generate tokens for local development

```sh
pnpm run tokens
```

## Build Storybook

```sh
pnpm run storybook:build
```

## Update API SDK

```sh
pnpm run update-api-sdk
```

## Build the Drata app

```sh
pnpm run app:drata:build
```

## Internationalization (i18n)

The `@globals/i18n` module provides a centralized internationalization system built on top of Lingui. It handles message extraction, compilation, and runtime locale switching with support for multiple languages including pseudo-localization for testing.

### Using i18n in Components

```tsx
// Import i18n utilities from the global module
import { t, Trans } from '@globals/i18n/macro';
import { i18n } from '@globals/i18n';

// Use Trans component macro for JSX content
<Trans>Welcome to Drata</Trans>

// Use t macro for string interpolation
const welcomeMessage = t`Welcome, ${userName}!`;

// Access the i18n instance for programmatic use
const currentLocale = i18n.locale;
```

### Available Locales

- **en** - English (source locale)
- **es** - Spanish
- **pseudo** - Pseudo-localization for testing UI with longer text

### i18n Workflow Commands

__NONE__

It is baked into the dev and build commands.

### Import Patterns

Always use the global i18n module instead of importing Lingui packages directly:

```ts
// ✅ Correct - Use global module
import { I18nProvider, i18n } from '@globals/i18n';
import { t, Trans } from '@globals/i18n/macro';

// ❌ Incorrect - Direct Lingui imports are banned
import { Trans } from '@lingui/react';
import { t } from '@lingui/core/macro';
```

### Dynamic Locale Loading

```ts
import { dynamicActivateLocale } from '@globals/i18n';

// Switch locale at runtime
await dynamicActivateLocale('es');
```

## Feature Flags & Access Control

For controlling feature visibility based on entitlements and feature flags, always use the Feature Access Model:

```ts
import { sharedFeatureAccessModel } from '@globals/feature-access';

// Check if a feature is enabled
if (sharedFeatureAccessModel.isFeatureEnabled) {
  // Show feature UI
}
```

## Troubleshooting

### Installation Issues

#### "Cannot resolve @drata packages"

```sh
# Make sure NPM_TOKEN is set for GitHub Package Registry
export NPM_TOKEN=your_github_token_here

# Clear pnpm cache and reinstall
pnpm store prune
pnpm install
```

#### "Wrong Node.js version"

```sh
# Use the correct Node.js version
nvm use
# or manually install Node.js v22.13.1
```

#### "pnpm command not found"

```sh
# Install pnpm globally
npm install -g pnpm@10.12.4
```

### Development Issues

#### "My eslint is not working?"

```sh
# Make sure you've installed dependencies
pnpm install

# Check if TypeScript compilation works
pnpm run typecheck
# If ^ fails, fix whatever problem it spit out at you

# Try running eslint manually
pnpm run lint

# VSCode Only
CMD+SHIFT+P - 'Restart ESLint Server'
```

#### "My typescript is not working?"

```sh
# Make sure you've installed dependencies
pnpm install

# Check TypeScript compilation
pnpm run typecheck
# If ^ fails, fix whatever problem it spit out at you

# VSCode Only
CMD+SHIFT+P - 'Restart TypeScript Server'
# You must open a typescript file first for it to start running
```

#### "API SDK types are missing"

```sh
# Update the API SDK
pnpm run update-api-sdk
```

#### "Design tokens are missing"

```sh
# Generate design tokens
pnpm run tokens
```
