import { snackbarController } from '@controllers/snackbar';
// eslint-disable-next-line no-restricted-imports -- It will not work if I use AppLink
import { Link } from '@cosmos/components/link';
import { SocketEvent } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, reaction, runInAction } from '@globals/mobx';
import { sharedSocketController } from '@globals/socket';
import { downloadFileFromSignedUrl } from '@helpers/download-file';

class AsyncDownloadAuditHubController {
    responseData: string | undefined;
    isInitialized = false;

    constructor() {
        makeAutoObservable(this);
    }

    init(): void {
        if (this.isInitialized) {
            console.debug('Async download controller already initialized');

            return;
        }

        reaction(
            () => sharedSocketController.isInitialized,
            (ready) => {
                if (!ready || this.isInitialized) {
                    return;
                }

                this.subscribeToChannel();
                runInAction(() => {
                    this.isInitialized = true;
                });
            },
            { fireImmediately: true },
        );
    }

    subscribeToChannel(): void {
        console.info('channel');
        sharedSocketController.subscribe({
            channelType: 'auditHub',
            eventName:
                SocketEvent.GENERATE_REQUEST_CONTROL_EVIDENCE_PACKAGE_COMPLETED,
            callback: (data: string) => {
                console.info('data subscribed', data);
                this.handleEvidence(data);
            },
        });

        sharedSocketController.subscribe({
            channelType: 'auditHub',
            eventName:
                SocketEvent.GENERATE_REQUEST_CONTROL_EVIDENCE_PACKAGE_FAILED,
            callback: () => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-evidence-package-failed`,
                    hasTimeout: false,
                    props: {
                        title: t`Failed to generate evidence package`,
                        description: t`An error occurred while generating your evidence package. Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        });
    }

    handleEvidence(data: string) {
        console.info('data subscribed', data);
        if (!data) {
            return;
        }

        const signedUrl = data;
        const timestamp = new Date().toISOString();

        snackbarController.addSnackbar({
            id: `${timestamp}-evidence-package-ready`,
            hasTimeout: false,
            props: {
                title: t`Your evidence package is ready`,
                description: t`Click the link below to download your evidence package`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
                link: Link({
                    label: t`Download`,
                    href: '#',
                    onClick: () => {
                        downloadFileFromSignedUrl(signedUrl);
                    },
                }),
            },
        });
        downloadFileFromSignedUrl(signedUrl);
    }
}

export const sharedAsyncDownloadAuditHubController =
    new AsyncDownloadAuditHubController();
