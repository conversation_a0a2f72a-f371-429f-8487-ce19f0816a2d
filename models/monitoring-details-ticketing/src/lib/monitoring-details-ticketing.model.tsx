import { openClosedTicketsModal } from '@components/utilities';
import { sharedConnectionsController } from '@controllers/connections';
import type { CreateTicketPayload } from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import {
    activeMonitoringDetailsController,
    sharedMonitoringDetailsTicketsController,
    sharedMonitoringDetailsTicketsMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import { DEFAULT_PARAMS } from '@cosmos/components/datatable';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { CreateTicketModalView } from '@views/create-ticket-modal';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

export class MonitoringDetailsTicketingModel {
    constructor() {
        makeAutoObservable(this);
    }

    createMonitorTicket = (
        payload: CreateTicketPayload,
        monitorId: number,
    ): void => {
        sharedMonitoringDetailsTicketsMutationController.createTicket(
            payload,
            monitorId,
        );
    };

    handleCreateTicket = (monitorId: number): void => {
        const workspaceName = sharedWorkspacesController.currentWorkspace?.name;

        const monitorName =
            sharedMonitoringTestDetailsController.testName ||
            activeMonitoringDetailsController.monitorDetailsData?.testName ||
            `Monitor ${monitorId}`;

        const defaultDescription = `Created in Drata for ${workspaceName}'s test: ${monitorName}`;

        this.openCreateTicketModal((payload: CreateTicketPayload) => {
            this.createMonitorTicket(payload, monitorId);
        }, defaultDescription);
    };

    handleViewClosedTickets = (monitorId: number): void => {
        sharedMonitoringDetailsTicketsController.loadClosedTicketsInfinite(
            monitorId,
        );

        openClosedTicketsModal({
            objectName: sharedMonitoringDetailsTicketsController.objectName,
            objectId: monitorId,
            ticketsController: sharedMonitoringDetailsTicketsController,
        });
    };

    openCreateTicketModal = (
        onCreateTicket: (payload: CreateTicketPayload) => void,
        defaultDescription?: string,
    ): void => {
        sharedTicketAutomationController.loadTicketAutomation(DEFAULT_PARAMS);
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        modalController.openModal({
            id: CREATE_TICKET_MODAL_ID,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: true,
            content: () => (
                <CreateTicketModalView
                    initData={{ description: defaultDescription }}
                    data-id="MonitorTicketModal"
                    onCreateTicket={onCreateTicket}
                />
            ),
        });
    };
}

export const sharedMonitoringDetailsTicketingModel =
    new MonitoringDetailsTicketingModel();
