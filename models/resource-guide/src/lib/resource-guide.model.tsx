import { isNil } from 'lodash-es';
import {
    AttributeDictionaryModal,
    SchemaModal,
} from '@components/resource-guide';
import { modalController } from '@controllers/modal';
import { sharedResourceGuideController } from '@controllers/resource-guide';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

const ATTRIBUTE_DICTIONARY_MODAL_ID = 'resource-attribute-dictionary-modal';

class ResourceGuideModel {
    constructor() {
        makeAutoObservable(this);
    }

    get selectedProvider(): ListBoxItemData | undefined {
        return sharedResourceGuideController.selectedProvider ?? undefined;
    }

    get selectedService(): ListBoxItemData | undefined {
        return sharedResourceGuideController.selectedService ?? undefined;
    }

    get selectedResource(): ListBoxItemData | undefined {
        return sharedResourceGuideController.selectedResource ?? undefined;
    }

    get availableProviders(): ListBoxItemData[] {
        return sharedResourceGuideController.availableProviders;
    }

    get availableServices(): ListBoxItemData[] {
        return sharedResourceGuideController.availableServices;
    }

    get availableResources(): ListBoxItemData[] {
        return sharedResourceGuideController.availableResources;
    }

    get currentSchema(): string | null {
        return sharedResourceGuideController.currentSchema;
    }

    get isLoadingServices(): boolean {
        return sharedResourceGuideController.isLoadingServices;
    }

    get isLoadingResources(): boolean {
        return sharedResourceGuideController.isLoadingResources;
    }

    get isLoadingSchema(): boolean {
        return sharedResourceGuideController.isLoadingSchema;
    }

    get isLoading(): boolean {
        return sharedResourceGuideController.isLoading;
    }

    get canShowSchema(): boolean {
        return sharedResourceGuideController.canShowSchema;
    }

    get emptyStateConfig() {
        if (isNil(this.selectedProvider)) {
            return {
                title: t`Select a provider to get started`,
                description: t`Choose a cloud provider to explore available services and resources.`,
            };
        }

        if (isNil(this.selectedResource)) {
            return {
                title: t`Select a resource to view its data structure`,
                description: t`Choose a resource to see an example of its data structure and available attributes.`,
            };
        }

        return null;
    }

    get providerSelectPlaceholder(): string {
        return t`Filter by provider`;
    }

    get serviceSelectPlaceholder(): string {
        return t`Filter by service`;
    }

    get resourceSelectPlaceholder(): string {
        return t`Choose a resource`;
    }

    get resourceSelectNoOptionsMessage(): string {
        if (isNil(this.selectedProvider)) {
            return t`You need to select a provider first.`;
        }

        return t`No options`;
    }

    get schemaHeaderText(): string {
        return t`Example data structure`;
    }

    get viewFullScreenTooltip(): string {
        return t`View full-screen`;
    }

    openSchemaModal = (): void => {
        modalController.openModal({
            id: 'resource-guide-schema-modal',
            content: () => <SchemaModal data-id="F2BGrOfO" />,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: false,
        });
    };

    closeSchemaModal = (): void => {
        modalController.closeModal('resource-guide-schema-modal');
    };

    openAttributeDictionaryModal = (): void => {
        modalController.openModal({
            id: ATTRIBUTE_DICTIONARY_MODAL_ID,
            content: () => <AttributeDictionaryModal data-id="5QV8apg-" />,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: false,
        });
    };

    closeAttributeDictionaryModal = (): void => {
        modalController.closeModal(ATTRIBUTE_DICTIONARY_MODAL_ID);
    };

    handleProviderChange = (option: ListBoxItemData | null): void => {
        if (!option) {
            return;
        }

        sharedResourceGuideController.selectProvider(option);
    };

    handleServiceChange = (option: ListBoxItemData | null): void => {
        if (option) {
            sharedResourceGuideController.selectService(option);
        }
    };

    handleResourceChange = (option: ListBoxItemData | null): void => {
        if (option) {
            sharedResourceGuideController.selectResource(option);
        }
    };

    handleOpenSchemaModal = (): void => {
        this.openSchemaModal();
    };

    handleAttributeDictionaryModal = (): void => {
        sharedResourceGuideController.loadAttributeDictionary();
        this.openAttributeDictionaryModal();
    };
}

export const sharedResourceGuideModel = new ResourceGuideModel();
