import { isEmpty, isNil } from 'lodash-es';
import {
    sharedAccessReviewActivePeriodUserTicketCreationController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { sharedConnectionsController } from '@controllers/connections';
import type { CreateTicketPayload } from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import type { UserAccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { openAccessReviewCreateTicketModal } from '../helpers/access-review-open-create-ticket-modal';
import { openAccessReviewUpdateStatusBulkActionsModal } from '../helpers/access-review-open-status-update-bulk-actions-modal';

class AccessReviewPersonnelTableModel {
    selectedArtifacts: UserAccessReviewPeriodApplicationResponseDto[] = [];
    selectedRows: number[] = [];
    isAllRowsSelected = false;
    dataTableRef: React.RefObject<DatatableRef> | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const { accessReviewPeriodApplicationUsersList } =
            sharedAccessReviewPeriodApplicationUsersController;
        const selectedIndices = Object.keys(selectedRows);
        const selectedRowData = accessReviewPeriodApplicationUsersList.filter(
            (applicationUser) =>
                selectedIndices.includes(
                    String(applicationUser.applicationUserId ?? ''),
                ),
        );

        this.selectedArtifacts = selectedRowData.filter(Boolean);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    setDataTableRef = (ref: React.RefObject<DatatableRef> | null): void => {
        this.dataTableRef = ref;
    };

    get bulkActions(): BulkAction[] {
        const { loadConfiguredConnectionsByProviderType } =
            sharedConnectionsController;
        const activeTicketingConnections =
            loadConfiguredConnectionsByProviderType('TICKETING');

        const { periodId, applicationId } =
            sharedAccessReviewPeriodApplicationUsersController;

        const { user: loggedUser } = sharedCurrentUserController;

        const actions: BulkAction[] = [
            {
                actionType: 'button',
                id: 'change-status-button',
                typeProps: {
                    label: t`Change status`,
                    level: 'tertiary',
                    onClick: () => {
                        openAccessReviewUpdateStatusBulkActionsModal(
                            this.selectedArtifacts
                                .map((user) => user.applicationUserId)
                                .filter((id): id is number => !isNil(id)),
                            this.dataTableRef,
                        );
                    },
                },
            },
        ];

        if (!isEmpty(activeTicketingConnections)) {
            actions.push({
                actionType: 'button',
                id: 'create-ticket-button',
                typeProps: {
                    label: t`Create ticket`,
                    level: 'tertiary',
                    onClick: () => {
                        openAccessReviewCreateTicketModal(
                            (payload: CreateTicketPayload) => {
                                sharedAccessReviewActivePeriodUserTicketCreationController.createAccessReviewTicket(
                                    payload,
                                    {
                                        applicationId: Number(applicationId),
                                        periodId: Number(periodId),
                                        userId: Number(loggedUser?.id),
                                    },
                                );
                                modalController.closeModal(
                                    'access-review-create-ticket-modal',
                                );
                            },
                            { description: t`Created in Drata` },
                        );
                    },
                },
            });
        }

        return actions;
    }
}

export const sharedAccessReviewPersonnelTableModel =
    new AccessReviewPersonnelTableModel();
