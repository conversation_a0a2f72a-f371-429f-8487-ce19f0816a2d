import type {
    CreateTicketFormData,
    CreateTicketPayload,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { CreateTicketModalView } from '@views/create-ticket-modal';

export const openAccessReviewCreateTicketModal = (
    onCreateTicket: (payload: CreateTicketPayload) => void,
    intData?: Partial<CreateTicketFormData>,
): void => {
    modalController.openModal({
        id: 'access-review-create-ticket-modal',
        content: () => (
            <CreateTicketModalView
                initData={intData}
                data-id="utilities-ticketing-for-access-review-active-period-user-create-ticket-modal"
                onCreateTicket={onCreateTicket}
            />
        ),
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};
