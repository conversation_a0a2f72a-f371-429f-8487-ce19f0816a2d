import {
    type CreateTicketFormData,
    type CreateTicketPayload,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { CreateTicketForm } from './components/create-ticket-form.component';
import { CREATE_TICKET_FORM_ID } from './constants/create-ticket.constants';

interface CreateTicketModalViewProps {
    onCreateTicket: (payload: CreateTicketPayload) => void;
    initData?: Partial<CreateTicketFormData>;
}

export const CreateTicketModalView = action(
    ({
        onCreateTicket,
        initData,
    }: CreateTicketModalViewProps): React.JSX.Element => {
        sharedCreateTicketController.initialize(onCreateTicket, initData);

        return (
            <>
                <Modal.Header title={t`Ticket Details`} />
                <Modal.Body>
                    <form
                        id={CREATE_TICKET_FORM_ID}
                        onSubmit={sharedCreateTicketController.createTicket}
                    >
                        <CreateTicketForm />
                    </form>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled:
                                sharedCreateTicketController.isCreating,
                            onClick: () => {
                                modalController.closeModal(
                                    'create-ticket-modal',
                                );
                            },
                        },
                        {
                            label: t`Create Ticket`,
                            form: CREATE_TICKET_FORM_ID,
                            level: 'primary',
                            type: 'submit',
                            isLoading: sharedCreateTicketController.isCreating,
                            // As requested by product we are disabling the submit button while loading any dropdown state
                            //  to prevent situations where the customer submits the form prematurely
                            cosmosUseWithCaution_isDisabled:
                                sharedCreateTicketController.isLoading,
                        },
                    ]}
                />
            </>
        );
    },
);
