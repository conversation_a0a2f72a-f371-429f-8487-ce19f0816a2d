import { useMemo } from 'react';
import { But<PERSON> } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedResourceGuideModel } from '@models/resource-guide';

/**
 * Resource Guide component that helps users explore cloud provider resources
 * and their data structures for monitoring test building.
 */
export const ResourceGuide = observer((): React.JSX.Element => {
    const model = sharedResourceGuideModel;

    const {
        handleAttributeDictionaryModal,
        handleProviderChange,
        handleServiceChange,
        handleResourceChange,
        handleOpenSchemaModal,
        canShowSchema,
        schemaHeaderText,
        viewFullScreenTooltip,
        currentSchema,
        emptyStateConfig,
        selectedProvider,
        availableProviders,
        isLoading,
        providerSelectPlaceholder,
        selectedService,
        availableServices,
        isLoadingServices,
        serviceSelectPlaceholder,
        selectedResource,
        availableResources,
        isLoadingResources,
        resourceSelectPlaceholder,
    } = model;

    // Render schema section when available
    const schemaSection = useMemo(() => {
        if (!canShowSchema) {
            return null;
        }

        return (
            <Stack direction="column" gap="3x" data-id="s08NylQJ">
                <Stack direction="row" justify="between" align="center">
                    <Text allowBold type="body" size="200">
                        {schemaHeaderText}
                    </Text>
                    <Stack direction="row" gap="2x">
                        <Button
                            isIconOnly
                            data-id="view-schema-fullscreen-button"
                            colorScheme="neutral"
                            level="tertiary"
                            size="sm"
                            startIconName="Expand"
                            label={viewFullScreenTooltip}
                            onClick={handleOpenSchemaModal}
                        />
                    </Stack>
                </Stack>

                {currentSchema && (
                    <CodeViewer
                        data-id="schema-fullscreen-content"
                        language="json"
                        value={currentSchema}
                        isEditable={false}
                    />
                )}
                <Stack>
                    <Button
                        data-id="view-attribute-dictionary-button"
                        label={t`View attribute dictionary`}
                        level="tertiary"
                        endIconName="Expand"
                        hasPadding={false}
                        onClick={handleAttributeDictionaryModal}
                    />
                </Stack>
            </Stack>
        );
    }, [
        canShowSchema,
        schemaHeaderText,
        viewFullScreenTooltip,
        currentSchema,
        handleOpenSchemaModal,
    ]);

    // Render empty state when no resource is selected
    const emptyStateSection = useMemo(() => {
        if (!emptyStateConfig) {
            return null;
        }

        return (
            <EmptyState
                title={emptyStateConfig.title}
                description={emptyStateConfig.description}
                data-id="resource-guide-empty-state"
            />
        );
    }, [emptyStateConfig]);

    return (
        <Card
            title={t`Resource guide`}
            data-testid="ResourceGuideAside"
            data-id="i15JRkCQ"
            body={
                <Stack direction="column" gap="4x">
                    <SelectField
                        data-id="provider-select-aside"
                        formId="provider-select"
                        label={t`Provider`}
                        loaderLabel={t`Loading providers`}
                        name="provider-select"
                        placeholder={providerSelectPlaceholder}
                        value={selectedProvider}
                        options={availableProviders}
                        isLoading={isLoading}
                        onChange={handleProviderChange}
                    />

                    <SelectField
                        data-id="service-select-aside"
                        formId="service-select"
                        label={t`Service`}
                        loaderLabel={t`Loading services`}
                        name="service-select"
                        placeholder={serviceSelectPlaceholder}
                        value={selectedService}
                        options={availableServices}
                        isLoading={isLoadingServices}
                        disabled={!selectedProvider}
                        onChange={handleServiceChange}
                    />

                    <SelectField
                        data-id="resource-select-aside"
                        formId="resource-select"
                        label={t`Resource`}
                        loaderLabel={t`Loading resources`}
                        name="resource-select"
                        placeholder={resourceSelectPlaceholder}
                        value={selectedResource}
                        options={availableResources}
                        isLoading={isLoadingResources}
                        disabled={!selectedProvider}
                        // noOptionsMessage={resourceSelectNoOptionsMessage}
                        onChange={handleResourceChange}
                    />

                    {schemaSection}
                    {emptyStateSection}
                </Stack>
            }
        />
    );
});
