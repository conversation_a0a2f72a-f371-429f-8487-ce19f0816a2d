import { isNil } from 'lodash-es';
import { z } from 'zod';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedMonitoringCustomTestController } from '@controllers/monitoring-details';
import { sharedProviderServicesController } from '@controllers/provider-services';
import type {
    ListBoxItemData,
    ListBoxItems,
} from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { providerTypes } from '@globals/providers';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringCreateTestModel } from '@models/monitoring-create-test';
import type { FormSchema, FormValues } from '@ui/forms';
import { ConditionsGroupField } from '../components/conditions-group-field.component';
import { ProviderSelectComponent } from '../components/provider-select.component';
import { ALLOWED_PROVIDER_TYPES } from '../constants/allowed-providers.constant';
import {
    type Operator,
    operatorHumanReadable,
    type OperatorValue,
    SUPPORTED_TYPES,
    type SupportedTypes,
} from '../constants/operator.constant';
import { buildAttributesRecursiveSchema } from '../helpers/build-attributes-recursive-schema.helper';
import { toCustomTestRequestDto } from '../helpers/form-values-reader.helper';
import type { ProviderOptions } from '../types/provider-options.type';

class MonitoringBuilderFormModel {
    selectedCategory: ListBoxItemData = this.defaultCategory;
    selectedProviderItem: ListBoxItemData | undefined = undefined;
    selectedAccounts: ListBoxItemData | ListBoxItemData[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    private getProviderResourcesParams(
        selectedProviderItem: ListBoxItemData | undefined,
    ): {
        workspaceId?: string;
        customClientAlias?: string;
    } {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!selectedProviderItem) {
            return {};
        }

        // Check if this is a custom provider by looking at the provider options
        const providerOption = this.providerOptions().find(
            (option) => option.value === selectedProviderItem.value,
        );

        const isCustomProvider = providerOption?.providerType.includes(
            providerTypes.CUSTOM_PROVIDER_TYPE.id,
        );

        return {
            workspaceId: workspaceId ? String(workspaceId) : undefined,
            customClientAlias: isCustomProvider
                ? selectedProviderItem.label
                : undefined,
        };
    }

    get formSchema(): FormSchema {
        return {
            logic: {
                type: 'group',
                header: 'Logic',
                fields: {
                    evaluationThreshold: {
                        type: 'select',
                        label: t`Evaluation threshold`,
                        helpText: t`This test will pass if all results meet the conditions below.`,
                        options: [
                            {
                                id: 'all-pass',
                                label: t`All results must pass`,
                                value: 'nofail',
                            },
                            {
                                id: 'at-least-one-pass',
                                label: t`At least one result must pass`,
                                value: 'hasonepass',
                            },
                            {
                                id: 'only-one-fail',
                                label: t`Only one result may fail`,
                                value: 'failonce',
                            },
                        ],
                        validator: z.object({
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        }),
                    },
                    category: {
                        type: 'select',
                        label: t`Category`,
                        options: this.categories,
                        initialValue: this.defaultCategory,
                    },
                    provider: {
                        type: 'custom',
                        label: t`Provider`,
                        validateWithDefault: 'select',
                        initialValue: undefined,
                        options: this.getProviderOptionsByCategory(),
                        render: ProviderSelectComponent,
                    },
                    accounts: {
                        type: 'combobox',
                        label: t`Accounts`,
                        options: this.accountsOptionsByProvider,
                        isMultiSelect: true,
                        loaderLabel: t`Loading`,
                        readOnly: this.selectedCategory.id === 'CUSTOM',
                        getSearchEmptyState: () => t`No results found`,
                    },
                    conditionGroups: {
                        type: 'custom',
                        customType: 'arrayOfObjects',
                        label: t`Condition groups`,
                        render: ConditionsGroupField,
                        initialValue: [
                            {
                                service: '',
                                resource: '',
                                hasFilteringCriteria: 'false',
                                criteria: '',
                                count: 1,
                                conditionSets: [
                                    {
                                        operatorLogical: 'AND',
                                        attributes: [
                                            {
                                                attribute: '',
                                                operator: '',
                                                value: '',
                                                level: 0,
                                                subFilteringCriteriaSets: [],
                                            },
                                        ],
                                    },
                                ],
                                filteringCriteriaSets: [
                                    {
                                        operatorLogical: 'AND',
                                        attributes: [
                                            {
                                                attribute: '',
                                                operator: '',
                                                value: '',
                                                level: 0,
                                                subFilteringCriteriaSets: [],
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                        fields: {
                            service: {
                                type: 'select',
                                label: t`Services`,
                                options: [],
                            },
                            resource: {
                                type: 'select',
                                label: t`Resources`,
                                options: [],
                            },
                            hasFilteringCriteria: {
                                type: 'radioGroup',
                                label: t`Filtering criteria`,
                                options: [
                                    { label: t`true`, value: 'true' },
                                    { label: t`false`, value: 'false' },
                                ],
                            },
                            criteria: {
                                type: 'toggleGroup',
                                options: [
                                    { label: t`Exclusion`, value: 'exclusion' },
                                    { label: t`Inclusion`, value: 'inclusion' },
                                ],
                            },
                            count: {
                                type: 'custom',
                                label: t`Attribute Count`,
                            },
                            conditionSets:
                                buildAttributesRecursiveSchema(
                                    t`Condition sets`,
                                ),
                            filteringCriteriaSets:
                                buildAttributesRecursiveSchema(
                                    t`Filtering criteria sets`,
                                    true,
                                ),
                        },
                    },
                },
            },
        };
    }

    createCustomTest = (values: FormValues): void => {
        const dto = toCustomTestRequestDto(
            values,
            this.accountsOptionsByProvider,
            sharedMonitoringCreateTestModel.testName,
            sharedMonitoringCreateTestModel.testDescription,
        );

        sharedMonitoringCustomTestController.createCustomTest(dto);
    };

    providerOptions(): ProviderOptions[] {
        const { allConfiguredConnections } = sharedConnectionsController;

        return allConfiguredConnections
            .filter((connection) =>
                connection.providerTypes.some((type) =>
                    ALLOWED_PROVIDER_TYPES.includes(type.value),
                ),
            )
            .map((connection) => ({
                id: connection.clientType,
                label: connection.providerTypes.some(
                    (type) =>
                        type.value === providerTypes.CUSTOM_PROVIDER_TYPE.id,
                )
                    ? (connection.alias ?? connection.name)
                    : connection.name,
                value: connection.clientType,
                alias: connection.alias ?? connection.clientId,
                providerType: connection.providerTypes.map(
                    (type) => type.value,
                ),
            }));
    }

    get categories(): ListBoxItems {
        return [
            this.defaultCategory,
            {
                id: providerTypes.CUSTOM_PROVIDER_TYPE.id,
                label: t`Custom`,
                value: providerTypes.CUSTOM_PROVIDER_TYPE.id,
            },
        ];
    }

    get defaultCategory(): ListBoxItemData {
        return {
            id: providerTypes.INFRASTRUCTURE_PROVIDER_TYPE.id,
            label: t`Infrastructure`,
            value: providerTypes.INFRASTRUCTURE_PROVIDER_TYPE.id,
        };
    }

    set setSelectedCategory(category: ListBoxItemData) {
        this.selectedCategory = category;
        this.selectedProviderItem = undefined;
    }

    getAttributesByProviderResource(
        selectedProvider: ListBoxItemData | undefined,
        selectedResource: ListBoxItemData | undefined,
    ): string | undefined {
        if (isNil(selectedResource) || isNil(selectedProvider)) {
            return;
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProvider);

        const response = sharedProviderServicesController.getProviderAttributes(
            selectedProvider.value as string,
            selectedResource.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return;
        }

        return response.resourceEvaluatorSchema ?? undefined;
    }

    getProviderOptionsByCategory(): ListBoxItemData[] {
        const providers = this.providerOptions();

        const distinctProviders = providers.filter(
            (connection, index, array) =>
                array.findIndex((item) => item.label === connection.label) ===
                index,
        );

        // sort providers before return it
        return distinctProviders
            .filter((provider) =>
                provider.providerType.includes(
                    this.selectedCategory.value as string,
                ),
            )
            .map((provider) => ({
                id: provider.id,
                label: provider.label,
                value: provider.value,
            }))
            .sort((a, b) => a.label.localeCompare(b.label));
    }

    getAttributesOptionsByProviderResource(
        selectedProvider: ListBoxItemData | undefined,
        selectedResource: ListBoxItemData | undefined,
    ): ListBoxItemData[] {
        if (isNil(selectedResource) || isNil(selectedProvider)) {
            return [];
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProvider);

        const response = sharedProviderServicesController.getProviderAttributes(
            selectedProvider.value as string,
            selectedResource.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return [];
        }

        const { properties } = response;

        return properties.map((property, i) => ({
            id: i.toString(),
            label: property.name,
            value: property.name,
        }));
    }

    getOperatorOptionsByProviderResourceAttribute(
        selectedProvider: ListBoxItemData | undefined,
        selectedResource: ListBoxItemData | undefined,
        selectedAttribute: ListBoxItemData | undefined,
    ): ListBoxItemData[] {
        if (
            isNil(selectedResource) ||
            isNil(selectedProvider) ||
            isNil(selectedAttribute)
        ) {
            return [];
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProvider);

        const response = sharedProviderServicesController.getProviderAttributes(
            selectedProvider.value as string,
            selectedResource.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return [];
        }

        const { properties } = response;

        const selectedProperty = properties.find(
            (property) => property.name === selectedAttribute.value,
        );

        if (isNil(selectedProperty)) {
            return [];
        }

        const filteredOperators = selectedProperty.operators.filter(
            (operator) => {
                return operator.valueTypes.some((value) => {
                    return SUPPORTED_TYPES.includes(value as SupportedTypes);
                });
            },
        );

        return filteredOperators.map((filteredOperator, i) => ({
            id: i.toString(),
            label: operatorHumanReadable[filteredOperator.operator as Operator],
            value: filteredOperator.operator,
        }));
    }

    getValueOptionsByProviderResourceAttribute(
        selectedProvider: ListBoxItemData | undefined,
        selectedResource: ListBoxItemData | undefined,
        selectedAttribute: ListBoxItemData | undefined,
        selectedOperator: ListBoxItemData | undefined,
    ): OperatorValue[] {
        if (
            isNil(selectedResource) ||
            isNil(selectedProvider) ||
            isNil(selectedAttribute) ||
            isNil(selectedOperator)
        ) {
            return [];
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProvider);

        const response = sharedProviderServicesController.getProviderAttributes(
            selectedProvider.value as string,
            selectedResource.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return [];
        }

        const { properties } = response;

        const selectedProperty = properties.find(
            (property) => property.name === selectedAttribute.value,
        );

        if (isNil(selectedProperty)) {
            return [];
        }

        const filteredOperators = selectedProperty.operators.find(
            (operator) => {
                return operator.operator === selectedOperator.value;
            },
        );

        return filteredOperators?.valueTypes as OperatorValue[];
    }

    getServicesOptionsByProvider(
        selectedProviderItem: ListBoxItemData | undefined,
    ): ListBoxItemData[] {
        if (isNil(selectedProviderItem)) {
            return [];
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProviderItem);

        const response = sharedProviderServicesController.getProviderResources(
            selectedProviderItem.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return [];
        }

        const { resources } = response;

        return resources
            .filter((service, index, resourcesList) => {
                return (
                    resourcesList.findIndex(
                        (item) => item.service === service.service,
                    ) === index
                );
            })
            .map((resource, index) => ({
                id: index.toString(),
                label: resource.service,
                value: resource.service,
            }));
    }

    getFirstResourceOptionByProviderService(
        selectedProviderItem: ListBoxItemData | undefined,
        selectedService: ListBoxItemData | undefined,
    ): ListBoxItemData | undefined {
        if (isNil(selectedProviderItem)) {
            return;
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProviderItem);

        const response = sharedProviderServicesController.getProviderResources(
            selectedProviderItem.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return;
        }

        const resources = this.getResourcesOptionsByProviderService(
            selectedProviderItem,
            selectedService,
        );

        return resources[0];
    }

    getResourcesOptionsByProviderService(
        selectedProviderItem: ListBoxItemData | undefined,
        selectedService: ListBoxItemData | undefined,
    ): ListBoxItemData[] {
        if (isNil(selectedProviderItem)) {
            return [];
        }

        const { workspaceId, customClientAlias } =
            this.getProviderResourcesParams(selectedProviderItem);

        const response = sharedProviderServicesController.getProviderResources(
            selectedProviderItem.value as string,
            workspaceId,
            customClientAlias,
        );

        if (isNil(response)) {
            return [];
        }

        const { resources } = response;

        const filteredResources = selectedService
            ? resources.filter(
                  (resource) => resource.service === selectedService.value,
              )
            : resources;

        return filteredResources.map((resource, index) => ({
            id: index.toString(),
            label: resource.name,
            value: resource.name,
        }));
    }

    get accountsOptionsByProvider(): ListBoxItemData[] {
        if (
            this.selectedCategory.id === providerTypes.CUSTOM_PROVIDER_TYPE.id
        ) {
            return [];
        }

        const providers = this.providerOptions();
        const currentProviders = this.getProviderOptionsByCategory();

        const selectedProvider =
            this.selectedProviderItem ?? currentProviders[0];

        const accounts = providers
            .filter((provider) => provider.id === selectedProvider.value)
            .map((connection: ProviderOptions) => ({
                id: connection.alias ?? connection.label,
                label: connection.alias ?? connection.label,
                value: connection.alias ?? connection.label,
            }));

        return accounts.filter(
            (account, index, array) =>
                array.findIndex((item) => item.label === account.label) ===
                index,
        );
    }
}

export const sharedMonitoringBuilderFormModel =
    new MonitoringBuilderFormModel();
