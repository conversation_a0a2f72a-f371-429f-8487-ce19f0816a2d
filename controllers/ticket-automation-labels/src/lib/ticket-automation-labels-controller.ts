import { ticketsControllerGetTicketLabelsOptions } from '@globals/api-sdk/queries';
import type { TicketDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationLabelsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationLabels = new ObservedQuery(
        ticketsControllerGetTicketLabelsOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationLabels.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationLabels.hasError;
    }

    get ticketAutomationLabelList(): TicketDataResponseDto[] {
        return this.ticketAutomationLabels.data?.data ?? [];
    }

    loadTicketAutomationLabels = ({
        connectionId,
    }: {
        connectionId: number;
    }) => {
        this.ticketAutomationLabels.load({
            query: { connectionId },
        });
    };
}

export const sharedTicketAutomationLabelsController =
    new TicketAutomationLabelsController();
