import { sharedConnectionsController } from '@controllers/connections';
import { modalController } from '@controllers/modal';
import {
    sharedTicketAutomationFoldersController,
    sharedTicketAutomationListsController,
    sharedTicketAutomationTeamsController,
    sharedTicketAutomationTicketTypesController,
    sharedTicketAutomationWorkspacesController,
} from '@controllers/ticket-automation';
import { sharedTicketAutomationIssueTypesController } from '@controllers/ticket-automation-issue-types';
import { sharedTicketAutomationProjectsController } from '@controllers/ticket-automation-projects';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { ticketsControllerCreateTicketMutation } from '@globals/api-sdk/queries';
import type { TicketMonitoringFieldResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import type { BaseProvider } from '@globals/providers';
import { mapConnectionToBoxItem } from './helpers/mapConnectionToBoxItem.helper';
import type { CreateTicketFormData } from './types/create-ticket-form-data.type';

export interface CreateTicketPayload {
    connectionId: number;
    workspaceId?: string;
    teamId?: string;
    projectId?: string;
    folderId?: string;
    listId?: string;
    issueTypeId?: string;
    ticketTypeId?: string;
    assigneeId?: string;
    description: string;
    subject?: string;
}

const PROVIDERS_WITH_WORKSPACE = ['ASANA', 'CLICKUP'];

class CreateTicketController {
    /**
     * Form data.
     */
    formData: CreateTicketFormData;
    constructor() {
        makeAutoObservable(this);
        this.formData = {
            description: t`Created in Drata`,
            subject: t`Created in Drata`,
            fields: [],
        };
    }

    /**
     * Callback function to handle ticket creation.
     */
    onCreateTicket: ((payload: CreateTicketPayload) => void) | null = null;

    initData: Partial<CreateTicketFormData> = {};

    /**
     * Initialize with callback and optional default description.
     */
    initialize = (
        onCreateTicket: (payload: CreateTicketPayload) => void,
        initData: Partial<CreateTicketFormData> = {},
    ) => {
        this.onCreateTicket = onCreateTicket;
        this.initData = initData;
        this.resetForm(initData);
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
    };

    resetForm = (data?: Partial<CreateTicketFormData>) => {
        this.formData = {
            description: t`Created in Drata`,
            subject: t`Created in Drata`,
            fields: [],
            ...data,
        };
        this.currentStep = 0;

        return this.formData;
    };

    get isLoading() {
        return (
            this.isConnectionsLoading ||
            this.isWorkspacesLoading ||
            this.isTeamsLoading ||
            this.isProjectsLoading ||
            this.isFoldersLoading ||
            this.isListsLoading ||
            this.isIssueTypesLoading ||
            this.isTicketTypesLoading ||
            this.isCreating
        );
    }

    // Connection field

    get isConnectionsLoading() {
        return sharedConnectionsController.isLoading;
    }

    get availableConnections() {
        return sharedConnectionsController.allTicketingConnectionWithWriteAccess.map(
            mapConnectionToBoxItem,
        );
    }

    get showConnectionField(): boolean {
        return this.availableConnections.length > 1;
    }

    get connectionId(): number | undefined {
        if (!this.showConnectionField) {
            const connectionId = Number(this.availableConnections[0]?.id);

            return Number.isNaN(connectionId) ? undefined : connectionId;
        }

        return this.formData.connectionId;
    }

    get selectedConnection():
        | (ListBoxItemData & { provider?: BaseProvider })
        | undefined {
        if (!this.connectionId) {
            return undefined;
        }

        return this.availableConnections.find(
            (conn) => conn.id === String(this.connectionId),
        );
    }

    get providerId(): string | undefined {
        return this.selectedConnection?.provider?.id;
    }

    setConnection = (connection: ListBoxItemData) => {
        const connectionId = Number(connection.id);

        this.formData.connectionId = connectionId;
        // Reset dependent fields
        this.resetForm({ ...this.initData, connectionId });

        if (this.providerId === 'LINEAR') {
            this.loadTeams(connectionId);
        } else if (
            this.providerId &&
            PROVIDERS_WITH_WORKSPACE.includes(this.providerId)
        ) {
            this.loadWorkspaces(connectionId);
        } else {
            this.loadProjects(connectionId);
        }
    };

    // Workspace field

    loadWorkspaces = (connectionId: number) => {
        sharedTicketAutomationWorkspacesController.loadTicketAutomationWorkspaces(
            connectionId,
        );
    };

    get isWorkspacesLoading() {
        return sharedTicketAutomationWorkspacesController.isLoading;
    }

    get workspaces(): ListBoxItemData[] {
        return sharedTicketAutomationWorkspacesController.ticketAutomationWorkspacesList.map(
            (workspace) => ({
                id: String(workspace.value),
                label: workspace.label,
                value: String(workspace.value),
            }),
        );
    }

    get selectedWorkspace(): ListBoxItemData | undefined {
        if (!this.formData.workspaceId) {
            return undefined;
        }

        return this.workspaces.find(
            (workspace) =>
                workspace.value === String(this.formData.workspaceId),
        );
    }

    // Team field

    loadTeams = (connectionId: number) => {
        sharedTicketAutomationTeamsController.loadTicketAutomationTeams(
            connectionId,
        );
    };

    get isTeamsLoading() {
        return sharedTicketAutomationTeamsController.isLoading;
    }

    get teams(): ListBoxItemData[] {
        return sharedTicketAutomationTeamsController.ticketAutomationTeamsList.map(
            (team) => ({
                id: String(team.value),
                label: team.label,
                value: String(team.value),
            }),
        );
    }

    get selectedTeam(): ListBoxItemData | undefined {
        if (!this.formData.teamId) {
            return undefined;
        }

        return this.teams.find(
            (team) => team.value === String(this.formData.teamId),
        );
    }

    setTeam = (team: ListBoxItemData) => {
        this.formData.teamId = team.id;
    };

    loadProjects = (connectionId: number, workspaceId?: string | null) => {
        sharedTicketAutomationProjectsController.loadTicketAutomationProjects(
            connectionId,
            workspaceId,
        );
    };

    get isProjectsLoading() {
        return sharedTicketAutomationProjectsController.isLoading;
    }

    get projects(): ListBoxItemData[] {
        return sharedTicketAutomationProjectsController.ticketAutomationProjectList.map(
            (project) => {
                let projectValue = project.value;

                // this endpoint sometimes returns project.value as a string and sometimes as an object like { projectId: string }
                try {
                    const parsed = JSON.parse(project.value) as {
                        projectId: string;
                    };

                    if (parsed.projectId) {
                        projectValue = parsed.projectId;
                    }
                } catch {
                    // not JSON, keep as is
                }

                return {
                    id: projectValue,
                    label: project.label,
                    value: projectValue,
                };
            },
        );
    }

    get selectedProject(): ListBoxItemData | undefined {
        if (!this.formData.projectId) {
            return undefined;
        }

        return this.projects.find(
            (project) => project.value === this.formData.projectId,
        );
    }

    // Folder field

    loadFolders = (connectionId: number, projectId: string) => {
        sharedTicketAutomationFoldersController.loadTicketAutomationFolders(
            connectionId,
            projectId,
        );
    };

    get isFoldersLoading() {
        return sharedTicketAutomationFoldersController.isLoading;
    }

    get folders(): ListBoxItemData[] {
        return sharedTicketAutomationFoldersController.ticketAutomationFoldersList.map(
            (folder) => ({
                id: folder.value,
                label: folder.label,
                value: folder.value,
            }),
        );
    }

    get selectedFolder(): ListBoxItemData | undefined {
        if (!this.formData.folderId) {
            return undefined;
        }

        return this.folders.find(
            (folder) => folder.value === this.formData.folderId,
        );
    }

    setFolder = (folder: ListBoxItemData) => {
        this.formData.folderId = folder.id;
    };

    // List field

    loadLists = (connectionId: number, projectId: string) => {
        sharedTicketAutomationListsController.loadTicketAutomationLists(
            connectionId,
            projectId,
        );
    };

    get isListsLoading() {
        return sharedTicketAutomationListsController.isLoading;
    }

    get lists(): ListBoxItemData[] {
        return sharedTicketAutomationListsController.ticketAutomationListsList.map(
            (list) => ({
                id: list.value,
                label: list.label,
                value: list.value,
            }),
        );
    }

    get selectedList(): ListBoxItemData | undefined {
        if (!this.formData.listId) {
            return undefined;
        }

        return this.lists.find((list) => list.value === this.formData.listId);
    }

    setList = (list: ListBoxItemData) => {
        this.formData.listId = list.id;
    };

    // Issue type field

    loadIssueTypes = (params: { connectionId: number; projectId: string }) => {
        sharedTicketAutomationIssueTypesController.loadTicketAutomationIssueTypes(
            params,
        );
    };

    get isIssueTypesLoading() {
        return sharedTicketAutomationIssueTypesController.isLoading;
    }

    get issueTypes(): ListBoxItemData[] {
        return sharedTicketAutomationIssueTypesController.ticketAutomationIssueTypeList.map(
            (type) => ({
                id: type.value,
                label: type.name,
                value: type.value,
            }),
        );
    }

    get selectedIssueType(): ListBoxItemData | undefined {
        if (!this.formData.issueTypeId) {
            return undefined;
        }

        return this.issueTypes.find(
            (type) => type.value === this.formData.issueTypeId,
        );
    }

    setIssueType = (issueType: ListBoxItemData) => {
        this.formData.issueTypeId = issueType.id;
    };

    // Ticket type field

    loadTicketTypes = (params: {
        connectionId?: number;
        projectId?: string;
        workspaceId?: string;
        listId?: string;
        teamId?: string;
    }) => {
        sharedTicketAutomationTicketTypesController.loadTicketAutomationTicketTypes(
            params,
        );
    };

    get isTicketTypesLoading() {
        return sharedTicketAutomationTicketTypesController.isLoading;
    }

    get ticketTypes(): ListBoxItemData[] {
        return sharedTicketAutomationTicketTypesController.ticketAutomationTicketTypesList.map(
            (type) => ({
                id: String(type.value),
                label: String(type.name),
                value: String(type.value),
            }),
        );
    }

    get selectedTicketType(): ListBoxItemData | undefined {
        if (!this.formData.ticketTypeId) {
            return undefined;
        }

        return this.ticketTypes.find(
            (type) => type.value === this.formData.ticketTypeId,
        );
    }

    setTicketType = (ticketType: ListBoxItemData) => {
        this.formData.ticketTypeId = ticketType.id;
    };

    // Description field

    get editFieldsConfig():
        | { connectionId: number; projectId: string; ticketTypeId?: string }
        | undefined {
        if (!this.connectionId || !this.selectedProject?.id) {
            return undefined;
        }

        if (!this.selectedIssueType?.id && !this.selectedTicketType?.id) {
            return undefined;
        }

        return {
            connectionId: this.connectionId,
            projectId: this.selectedProject.id,
            ticketTypeId:
                this.selectedIssueType?.id || this.selectedTicketType?.id || '',
        };
    }

    get fields(): TicketMonitoringFieldResponseDto[] {
        return this.formData.fields;
    }

    setField = (field: TicketMonitoringFieldResponseDto): void => {
        const existingFieldIndex = this.formData.fields.findIndex(
            (f) => f.name === field.name,
        );

        if (existingFieldIndex !== -1) {
            this.formData.fields[existingFieldIndex] = field;

            return;
        }

        this.formData.fields.push(field);
    };

    get description(): string | undefined {
        return this.formData.description;
    }

    setDescription = (description: string) => {
        this.formData.description = description;
    };

    /**
     * Wizard steps (0-based).
     */
    currentStep = 0;

    nextStep = () => {
        if (this.currentStep < 2) {
            this.currentStep = this.currentStep + 1;
        }
    };

    previousStep = () => {
        if (this.currentStep > 0) {
            this.currentStep = this.currentStep - 1;
        }
    };

    get canProceedFromStep1(): boolean {
        return Boolean(this.connectionId && this.formData.projectId);
    }

    get canProceedFromStep2(): boolean {
        return Boolean(this.formData.issueTypeId);
    }

    get canCreateTicket(): boolean {
        return Boolean(
            this.connectionId &&
                this.formData.projectId &&
                this.formData.issueTypeId,
        );
    }

    /**
     * Mutation for creating ticket.
     */
    createTicketMutation = new ObservedMutation(
        ticketsControllerCreateTicketMutation,
    );

    get isCreating(): boolean {
        return this.createTicketMutation.isPending;
    }

    createTicket = () => {
        if (!this.canCreateTicket || !this.onCreateTicket) {
            return;
        }

        const payload: CreateTicketPayload = {
            connectionId: this.connectionId || 0,
            workspaceId: this.formData.workspaceId || '',
            projectId: this.formData.projectId || '',
            folderId: this.formData.folderId || '',
            listId: this.formData.listId || '',
            issueTypeId: this.formData.issueTypeId || '',
            ticketTypeId: this.formData.ticketTypeId || '',
            subject: this.formData.subject || '',
            description: this.formData.description || '',
        };

        this.onCreateTicket(payload);

        this.closeModal();
    };

    get hasError(): boolean {
        return this.createTicketMutation.hasError;
    }

    get error(): Error | null {
        return this.createTicketMutation.error;
    }

    closeModal = () => {
        modalController.closeModal('create-ticket-modal');
    };
}

export const sharedCreateTicketController = new CreateTicketController();
