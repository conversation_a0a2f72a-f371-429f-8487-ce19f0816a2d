import { ticketsControllerGetTicketAssignableOptions } from '@globals/api-sdk/queries';
import type { TicketUserResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationAssigneesController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationAssignees = new ObservedQuery(
        ticketsControllerGetTicketAssignableOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationAssignees.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationAssignees.hasError;
    }

    get ticketAutomationAssigneeList(): TicketUserResponseDto[] {
        return this.ticketAutomationAssignees.data?.data ?? [];
    }

    loadTicketAutomationAssignees = ({
        connectionId,
        projectId,
    }: {
        connectionId: number;
        projectId: number;
    }) => {
        this.ticketAutomationAssignees.load({
            path: { projectId },
            query: { connectionId },
        });
    };
}

export const sharedTicketAutomationAssigneesController =
    new TicketAutomationAssigneesController();
